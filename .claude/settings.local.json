{"permissions": {"allow": ["Bash(bun add:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(cp:*)", "Bash(bun install:*)", "Bash(bun:*)", "Bash(grep:*)", "Bash(NEXT_PUBLIC_SERVER_URL=http://localhost:3002 bun dev --port=3003)", "Bash(rg:*)", "Bash(find:*)", "<PERSON><PERSON>(touch:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(git push:*)", "Bash(gh repo create:*)", "Bash(git remote add:*)", "Bash(gh repo view:*)", "Bash(git ls-tree:*)", "Bash(node:*)", "<PERSON><PERSON>(curl:*)", "Bash(PGPASSWORD=postgres psql -h 127.0.0.1 -p 54322 -U postgres -d postgres -c \"\nINSERT INTO workspaces (id, name, slug, plan, created_at, updated_at) \nVALUES (''default-workspace-id'', ''Default Workspace'', ''default'', ''FREE'', NOW(), NOW()) \nON CONFLICT (slug) DO NOTHING;\n\nINSERT INTO users (id, email, full_name, workspace_id, role, created_at, updated_at) \nVALUES (''system-user-id'', ''<EMAIL>'', ''System User'', ''default-workspace-id'', ''ADMIN'', NOW(), NOW()) \nON CONFLICT (email) DO NOTHING;\n\nSELECT ''Workspace:'' as type, id, name FROM workspaces WHERE slug = ''default''\nUNION ALL\nSELECT ''User:'' as type, id, full_name FROM users WHERE email = ''<EMAIL>'';\n\")", "Bash(PGPASSWORD=postgres psql -h 127.0.0.1 -p 54322 -U postgres -d postgres -c \"\\dt\")", "Bash(PGPASSWORD=postgres psql -h 127.0.0.1 -p 54322 -U postgres -d postgres -f /Users/<USER>/Downloads/Coding/IBC/CMA/apps/server/supabase/migrations/001_initial_schema.sql)", "Bash(PGPASSWORD=postgres psql -h 127.0.0.1 -p 54322 -U postgres -d postgres -c \"\nINSERT INTO workspaces (id, name, slug, plan, created_at, updated_at) \nVALUES (''01234567-89ab-cdef-0123-456789abcdef'', ''Default Workspace'', ''default'', ''free'', NOW(), NOW()) \nON CONFLICT (slug) DO NOTHING;\n\nINSERT INTO users (id, email, full_name, workspace_id, role, created_at, updated_at) \nVALUES (''fedcba98-7654-3210-fedc-ba9876543210'', ''<EMAIL>'', ''System User'', ''01234567-89ab-cdef-0123-456789abcdef'', ''admin'', NOW(), NOW()) \nON CONFLICT (email) DO NOTHING;\n\nSELECT ''Created workspace:'' as message, id, name FROM workspaces WHERE slug = ''default''\nUNION ALL\nSELECT ''Created user:'' as message, id, full_name FROM users WHERE email = ''<EMAIL>'';\n\")", "Bash(rm:*)", "<PERSON><PERSON>(pkill:*)", "Bash(ls:*)"], "deny": []}}