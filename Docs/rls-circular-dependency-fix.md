# RLS Circular Dependency Fix

## Issue Analysis

### Root Cause
The PostgreSQL RLS (Row Level Security) policies were creating infinite recursion due to circular dependencies. The problem occurred when RLS policies on tables like `workspaces`, `projects`, `reports`, etc. tried to query the `users` table to get the current user's `workspace_id`, but the `users` table itself had RLS enabled.

### The Circular Dependency Chain
```
1. User queries workspaces table
2. RLS policy on workspaces queries users table to get workspace_id
3. RLS policy on users table is triggered
4. This creates infinite recursion
```

### Problematic Policies
The following policies were causing circular dependencies:

```sql
-- ❌ PROBLEMATIC: Queries users table from within RLS policy
CREATE POLICY "Users can view their own workspace"
  ON workspaces FOR SELECT
  USING (id = (
    SELECT workspace_id FROM users WHERE id = auth.uid() LIMIT 1  -- Circular dependency!
  ));
```

## Solution

### Security Definer Function Approach
We created a `SECURITY DEFINER` function that bypasses RLS when getting the current user's workspace_id:

```sql
CREATE OR REPLACE FUNCTION get_current_user_workspace_id()
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER  -- This bypasses RLS
SET search_path = public
AS $$
DECLARE
  user_workspace_id UUID;
BEGIN
  -- Get workspace_id for current user, bypassing RLS
  SELECT workspace_id INTO user_workspace_id 
  FROM users 
  WHERE id = auth.uid() 
  LIMIT 1;
  
  RETURN user_workspace_id;
END;
$$;
```

### Updated Policies
All RLS policies now use the security definer function:

```sql
-- ✅ FIXED: Uses security definer function
CREATE POLICY "Users can view their own workspace"
  ON workspaces FOR SELECT
  USING (id = get_current_user_workspace_id());
```

## How to Apply the Fix

### Option 1: Using Supabase CLI (Recommended)
If you're using local Supabase development:

```bash
# Navigate to server directory
cd apps/server

# Apply the migration
supabase db reset
# or
supabase migration up
```

### Option 2: Using Prisma (Current Setup)
Since your project uses Prisma with Supabase:

```bash
# Apply the database changes
bun db:push
```

### Option 3: Manual SQL Execution
If you're using hosted Supabase, execute the migration manually:

1. Go to your Supabase dashboard → SQL Editor
2. Execute the contents of `apps/server/supabase/migrations/004_fix_rls_circular_dependency.sql`

## Verification

### 1. Check Function Creation
```sql
SELECT proname, prosecdef 
FROM pg_proc 
WHERE proname = 'get_current_user_workspace_id';
```

### 2. Test RLS Policies
```sql
-- This should work without infinite recursion
SELECT * FROM workspaces LIMIT 1;
SELECT * FROM projects LIMIT 1;
SELECT * FROM reports LIMIT 1;
```

### 3. Check Logs
The fix includes robust logging. Check your PostgreSQL logs for:
```
LOG: get_current_user_workspace_id called for user: [user-id]
LOG: get_current_user_workspace_id returning: [workspace-id]
```

## Security Considerations

### Why SECURITY DEFINER is Safe Here
1. **Limited Scope**: The function only returns the current user's workspace_id
2. **No Data Exposure**: It doesn't expose other users' data
3. **Authentication Required**: Only works with authenticated users (`auth.uid()`)
4. **Read-Only**: The function only reads data, doesn't modify anything

### Maintained Security
- Users can still only see their own workspace data
- Cross-workspace access is prevented
- All other security policies remain intact

## Debugging

### Enable Detailed Logging
The fix includes logging functions. To use them in your policies:

```sql
-- Example usage in a policy
CREATE POLICY "Debug policy"
  ON some_table FOR SELECT
  USING (
    log_rls_access('some_table', 'SELECT', auth.uid()) IS NULL AND
    workspace_id = get_current_user_workspace_id()
  );
```

### Common Issues
1. **Function Not Found**: Ensure the migration was applied successfully
2. **Permission Denied**: Check that `GRANT EXECUTE` was applied
3. **Still Getting Recursion**: Verify all old policies were dropped

## Files Modified
- `apps/server/supabase/migrations/002_rls_policies.sql` - Updated existing policies
- `apps/server/supabase/migrations/004_fix_rls_circular_dependency.sql` - New migration with fix
- `Docs/rls-circular-dependency-fix.md` - This documentation

## Next Steps
1. Apply the migration using `bun db:push`
2. Test your application to ensure RLS works correctly
3. Monitor logs for any remaining issues
4. Consider adding more specific logging if needed for debugging
