-- Fix for RLS Circular Dependency Issue
-- This migration addresses the infinite recursion problem in PostgreSQL RLS policies

-- First, drop all existing policies that have circular dependencies
DROP POLICY IF EXISTS "Users can view their own workspace" ON workspaces;
DROP POLICY IF EXISTS "Users can view projects in their workspace" ON projects;
DROP POLICY IF EXISTS "Users can create projects in their workspace" ON projects;
DROP POLICY IF EXISTS "Users can update projects in their workspace" ON projects;
DROP POLICY IF EXISTS "Users can view reports in their workspace" ON reports;
DROP POLICY IF EXISTS "Users can create reports in their workspace" ON reports;
DROP POLICY IF EXISTS "Users can update reports in their workspace" ON reports;
DROP POLICY IF EXISTS "Users can view agent runs for their workspace reports" ON agent_runs;
DROP POLICY IF EXISTS "Users can view data sources for their workspace projects" ON data_sources;
DROP POLICY IF EXISTS "Users can view embeddings for their workspace projects" ON embeddings;

-- Drop the function if it exists (for idempotency)
DROP FUNCTION IF EXISTS get_current_user_workspace_id();

-- Create security definer function to safely get current user's workspace_id
-- This function bypasses RLS to prevent circular dependencies
CREATE OR REPLACE FUNCTION get_current_user_workspace_id()
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  user_workspace_id UUID;
BEGIN
  -- Log function call for debugging
  RAISE LOG 'get_current_user_workspace_id called for user: %', auth.uid();
  
  -- Get workspace_id for current user, bypassing RLS
  SELECT workspace_id INTO user_workspace_id 
  FROM users 
  WHERE id = auth.uid() 
  LIMIT 1;
  
  -- Log result for debugging
  RAISE LOG 'get_current_user_workspace_id returning: %', user_workspace_id;
  
  RETURN user_workspace_id;
EXCEPTION
  WHEN OTHERS THEN
    -- Log any errors for debugging
    RAISE LOG 'Error in get_current_user_workspace_id: %', SQLERRM;
    RETURN NULL;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_current_user_workspace_id() TO authenticated;

-- Recreate all RLS policies using the security definer function

-- RLS Policies for workspaces (using security definer function)
CREATE POLICY "Users can view their own workspace"
  ON workspaces FOR SELECT
  USING (id = get_current_user_workspace_id());

-- RLS Policies for projects (using security definer function)
CREATE POLICY "Users can view projects in their workspace"
  ON projects FOR SELECT
  USING (workspace_id = get_current_user_workspace_id());

CREATE POLICY "Users can create projects in their workspace"
  ON projects FOR INSERT
  WITH CHECK (workspace_id = get_current_user_workspace_id());

CREATE POLICY "Users can update projects in their workspace"
  ON projects FOR UPDATE
  USING (workspace_id = get_current_user_workspace_id());

-- RLS Policies for reports (using security definer function)
CREATE POLICY "Users can view reports in their workspace"
  ON reports FOR SELECT
  USING (workspace_id = get_current_user_workspace_id());

CREATE POLICY "Users can create reports in their workspace"
  ON reports FOR INSERT
  WITH CHECK (workspace_id = get_current_user_workspace_id());

CREATE POLICY "Users can update reports in their workspace"
  ON reports FOR UPDATE
  USING (workspace_id = get_current_user_workspace_id());

-- RLS Policies for agent_runs (using security definer function)
CREATE POLICY "Users can view agent runs for their workspace reports"
  ON agent_runs FOR SELECT
  USING (report_id IN (
    SELECT id FROM reports WHERE workspace_id = get_current_user_workspace_id()
  ));

-- RLS Policies for data_sources (using security definer function)
CREATE POLICY "Users can view data sources for their workspace projects"
  ON data_sources FOR SELECT
  USING (project_id IN (
    SELECT id FROM projects WHERE workspace_id = get_current_user_workspace_id()
  ));

-- RLS Policies for embeddings (using security definer function)
CREATE POLICY "Users can view embeddings for their workspace projects"
  ON embeddings FOR SELECT
  USING (project_id IN (
    SELECT id FROM projects WHERE workspace_id = get_current_user_workspace_id()
  ));

-- Add console logging for debugging RLS policy execution
-- This will help identify any remaining issues
CREATE OR REPLACE FUNCTION log_rls_access(table_name TEXT, operation TEXT, user_id UUID)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RAISE LOG 'RLS Access: table=%, operation=%, user=%, workspace=%', 
    table_name, operation, user_id, get_current_user_workspace_id();
END;
$$;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION log_rls_access(TEXT, TEXT, UUID) TO authenticated;
