-- Create security definer function to safely get current user's workspace_id
-- This function bypasses RLS to prevent circular dependencies
CREATE OR REPLACE FUNCTION get_current_user_workspace_id()
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  user_workspace_id UUID;
BEGIN
  -- Log function call for debugging
  RAISE LOG 'get_current_user_workspace_id called for user: %', auth.uid();

  -- Get workspace_id for current user, bypassing RLS
  SELECT workspace_id INTO user_workspace_id
  FROM users
  WHERE id = auth.uid()
  LIMIT 1;

  -- Log result for debugging
  RAISE LOG 'get_current_user_workspace_id returning: %', user_workspace_id;

  RETURN user_workspace_id;
EXCEPTION
  WHEN OTHERS THEN
    -- Log any errors for debugging
    RAISE LOG 'Error in get_current_user_workspace_id: %', SQLERRM;
    RETURN NULL;
END;
$$;

-- <PERSON><PERSON> Policies for users (no circular dependency)
CREATE POLICY "Users can view themselves"
  ON users FOR SELECT
  USING (id = auth.uid());

-- REMOVED: This policy created infinite recursion
-- Users can only view themselves to avoid circular dependencies
-- If workspace users need to be viewed, handle it in application layer

-- RLS Policies for workspaces (using security definer function)
CREATE POLICY "Users can view their own workspace"
  ON workspaces FOR SELECT
  USING (id = get_current_user_workspace_id());

CREATE POLICY "Users can update their own profile"
  ON users FOR UPDATE
  USING (id = auth.uid());

-- RLS Policies for projects (using security definer function)
CREATE POLICY "Users can view projects in their workspace"
  ON projects FOR SELECT
  USING (workspace_id = get_current_user_workspace_id());

CREATE POLICY "Users can create projects in their workspace"
  ON projects FOR INSERT
  WITH CHECK (workspace_id = get_current_user_workspace_id());

CREATE POLICY "Users can update projects in their workspace"
  ON projects FOR UPDATE
  USING (workspace_id = get_current_user_workspace_id());

-- RLS Policies for reports (using security definer function)
CREATE POLICY "Users can view reports in their workspace"
  ON reports FOR SELECT
  USING (workspace_id = get_current_user_workspace_id());

CREATE POLICY "Users can create reports in their workspace"
  ON reports FOR INSERT
  WITH CHECK (workspace_id = get_current_user_workspace_id());

CREATE POLICY "Users can update reports in their workspace"
  ON reports FOR UPDATE
  USING (workspace_id = get_current_user_workspace_id());

-- RLS Policies for agent_runs (using security definer function)
CREATE POLICY "Users can view agent runs for their workspace reports"
  ON agent_runs FOR SELECT
  USING (report_id IN (
    SELECT id FROM reports WHERE workspace_id = get_current_user_workspace_id()
  ));

CREATE POLICY "System can manage agent runs"
  ON agent_runs FOR ALL
  USING (true);

-- RLS Policies for data_sources (using security definer function)
CREATE POLICY "Users can view data sources for their workspace projects"
  ON data_sources FOR SELECT
  USING (project_id IN (
    SELECT id FROM projects WHERE workspace_id = get_current_user_workspace_id()
  ));

CREATE POLICY "System can manage data sources"
  ON data_sources FOR ALL
  USING (true);

-- RLS Policies for embeddings (using security definer function)
CREATE POLICY "Users can view embeddings for their workspace projects"
  ON embeddings FOR SELECT
  USING (project_id IN (
    SELECT id FROM projects WHERE workspace_id = get_current_user_workspace_id()
  ));

CREATE POLICY "System can manage embeddings"
  ON embeddings FOR ALL
  USING (true);