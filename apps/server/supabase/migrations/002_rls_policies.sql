-- RLS Policies for users (no circular dependency)
CREATE POLICY "Users can view themselves"
  ON users FOR SELECT
  USING (id = auth.uid());

-- REMOVED: This policy created infinite recursion
-- Users can only view themselves to avoid circular dependencies
-- If workspace users need to be viewed, handle it in application layer

-- RLS Policies for workspaces (simplified, no user dependency)
CREATE POLICY "Users can view their own workspace"
  ON workspaces FOR SELECT
  USING (id = (
    SELECT workspace_id FROM users WHERE id = auth.uid() LIMIT 1
  ));

CREATE POLICY "Users can update their own profile"
  ON users FOR UPDATE
  USING (id = auth.uid());

-- RLS Policies for projects (simplified)
CREATE POLICY "Users can view projects in their workspace"
  ON projects FOR SELECT
  USING (workspace_id = (
    SELECT workspace_id FROM users WHERE id = auth.uid() LIMIT 1
  ));

CREATE POLICY "Users can create projects in their workspace"
  ON projects FOR INSERT
  WITH CHECK (workspace_id = (
    SELECT workspace_id FROM users WHERE id = auth.uid() LIMIT 1
  ));

CREATE POLICY "Users can update projects in their workspace"
  ON projects FOR UPDATE
  USING (workspace_id = (
    SELECT workspace_id FROM users WHERE id = auth.uid() LIMIT 1
  ));

-- RLS Policies for reports (simplified)
CREATE POLICY "Users can view reports in their workspace"
  ON reports FOR SELECT
  USING (workspace_id = (
    SELECT workspace_id FROM users WHERE id = auth.uid() LIMIT 1
  ));

CREATE POLICY "Users can create reports in their workspace"
  ON reports FOR INSERT
  WITH CHECK (workspace_id = (
    SELECT workspace_id FROM users WHERE id = auth.uid() LIMIT 1
  ));

CREATE POLICY "Users can update reports in their workspace"
  ON reports FOR UPDATE
  USING (workspace_id = (
    SELECT workspace_id FROM users WHERE id = auth.uid() LIMIT 1
  ));

-- RLS Policies for agent_runs (simplified)
CREATE POLICY "Users can view agent runs for their workspace reports"
  ON agent_runs FOR SELECT
  USING (report_id IN (
    SELECT id FROM reports WHERE workspace_id = (
      SELECT workspace_id FROM users WHERE id = auth.uid() LIMIT 1
    )
  ));

CREATE POLICY "System can manage agent runs"
  ON agent_runs FOR ALL
  USING (true);

-- RLS Policies for data_sources (simplified)
CREATE POLICY "Users can view data sources for their workspace projects"
  ON data_sources FOR SELECT
  USING (project_id IN (
    SELECT id FROM projects WHERE workspace_id = (
      SELECT workspace_id FROM users WHERE id = auth.uid() LIMIT 1
    )
  ));

CREATE POLICY "System can manage data sources"
  ON data_sources FOR ALL
  USING (true);

-- RLS Policies for embeddings (simplified)
CREATE POLICY "Users can view embeddings for their workspace projects"
  ON embeddings FOR SELECT
  USING (project_id IN (
    SELECT id FROM projects WHERE workspace_id = (
      SELECT workspace_id FROM users WHERE id = auth.uid() LIMIT 1
    )
  ));

CREATE POLICY "System can manage embeddings"
  ON embeddings FOR ALL
  USING (true);