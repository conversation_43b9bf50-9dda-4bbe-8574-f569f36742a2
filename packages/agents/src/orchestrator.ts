import { BaseAgent } from './base';
import type { AgentContext, AgentResult } from './base';
import { LeadResearchAgent } from './lead-research';
import { OnChainAnalyticsAgent } from './onchain-analytics';
import { SocialSentimentAgent } from './social-sentiment';
import { CompetitorAnalysisAgent } from './competitor-analysis';
import { MarketPositioningAgent } from './market-positioning';
import { ReportGenerationEngine } from './report-generation';
import { createServerClient } from '@cma/database';

export interface OrchestrationResult {
  success: boolean;
  results: Record<string, AgentResult>;
  reportData: any;
  error?: string;
}

export class AgentOrchestrator {
  private db = createServerClient();
  private agents: Map<string, BaseAgent> = new Map();
  private reportEngine: ReportGenerationEngine;

  constructor() {
    this.initializeAgents();
    this.reportEngine = new ReportGenerationEngine();
  }

  private initializeAgents(): void {
    console.log('🤖 Initializing agents...');
    this.agents.set('lead_research', new LeadResearchAgent());
    this.agents.set('onchain_analytics', new OnChainAnalyticsAgent());
    this.agents.set('social_sentiment', new SocialSentimentAgent());
    this.agents.set('competitor_analysis', new CompetitorAnalysisAgent());
    this.agents.set('market_positioning', new MarketPositioningAgent());
    console.log(`✅ Initialized ${this.agents.size} agents`);
  }

  async orchestrateResearch(context: AgentContext, projectInput: any): Promise<OrchestrationResult> {
    console.log(`🚀 Starting orchestration for report ${context.reportId}`);
    console.log(`📋 Project input:`, projectInput);
    console.log(`🎯 Report depth: ${context.reportDepth}`);

    try {
      console.log(`📊 Updating report status to 'generating'`);
      await this.updateReportStatus(context.reportId, 'generating');

      // Step 1: Lead Research Agent analyzes input and creates research plan
      console.log(`🔍 Starting lead research agent...`);
      const leadAgent = this.agents.get('lead_research')!;
      const leadResult = await leadAgent.execute(context, projectInput);

      console.log(`📝 Lead research result:`, {
        success: leadResult.success,
        error: leadResult.error,
        hasData: !!leadResult.data,
      });

      if (!leadResult.success) {
        console.error(`❌ Lead research failed:`, leadResult.error);
        await this.updateReportStatus(context.reportId, 'failed');
        return {
          success: false,
          results: { lead_research: leadResult },
          reportData: null,
          error: 'Lead research failed: ' + leadResult.error,
        };
      }

      const researchPlan = leadResult.data;
      
      // Step 2: Execute specialized agents in parallel based on research plan
      const agentTasks = this.createAgentTasks(context, researchPlan);
      const agentResults = await this.executeAgentsInParallel(agentTasks);

      // Step 3: Synthesize results into comprehensive report
      const reportData = await this.synthesizeResults(context, agentResults);

      // Step 4: Generate formatted report using ReportGenerationEngine
      console.log('📄 Generating formatted report...');
      const formattedReport = await this.reportEngine.generateReport(
        context.projectId,
        agentResults,
        {
          format: 'markdown',
          template_id: 'detailed_analysis',
          include_charts: true,
        }
      );
      console.log('✅ Formatted report generated successfully');

      // Step 5: Update report with final data and formatted content
      await this.updateReportWithResults(context.reportId, reportData, formattedReport);
      await this.updateReportStatus(context.reportId, 'completed');

      return {
        success: true,
        results: agentResults,
        reportData,
      };

    } catch (error) {
      await this.updateReportStatus(context.reportId, 'failed');
      console.error('Orchestration failed:', error);
      
      return {
        success: false,
        results: {},
        reportData: null,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  private createAgentTasks(context: AgentContext, researchPlan: any): Array<{
    agentType: string;
    agent: BaseAgent;
    input: any;
  }> {
    const tasks = [];

    // Determine which agents to run based on research plan and report depth
    const agentConfigs = [
      { type: 'onchain_analytics', required: true },
      { type: 'social_sentiment', required: true },
      { type: 'competitor_analysis', required: context.reportDepth !== 'quick' },
      { type: 'market_positioning', required: true },
    ];

    console.log(`📋 Agent configs for ${context.reportDepth} report:`, agentConfigs.filter(c => c.required).map(c => c.type));

    for (const config of agentConfigs) {
      if (config.required) {
        const agent = this.agents.get(config.type);
        if (agent) {
          tasks.push({
            agentType: config.type,
            agent,
            input: {
              ...researchPlan,
              agentSpecificData: researchPlan[config.type] || {},
            },
          });
        }
      }
    }

    return tasks;
  }

  private async executeAgentsInParallel(tasks: Array<{
    agentType: string;
    agent: BaseAgent;
    input: any;
  }>): Promise<Record<string, AgentResult>> {
    const results: Record<string, AgentResult> = {};

    // Execute agents in parallel with error handling
    const promises = tasks.map(async (task) => {
      try {
        const result = await task.agent.execute(task.input.context, task.input);
        results[task.agentType] = result;
      } catch (error) {
        results[task.agentType] = {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
        };
      }
    });

    await Promise.allSettled(promises);
    return results;
  }

  private async synthesizeResults(
    context: AgentContext,
    agentResults: Record<string, AgentResult>
  ): Promise<any> {
    // Transform agent results to frontend-compatible format
    const agentsResultsArray = await this.transformAgentResultsForFrontend(agentResults);
    
    // Calculate overall score based on agent scores
    const overallScore = this.calculateOverallScore(agentsResultsArray);
    
    // Create comprehensive report structure
    const reportData = {
      // Preserve original agent results for frontend
      agents_results: agentsResultsArray,
      overall_score: overallScore,
      
      // Synthesized data fields
      executive_summary: this.generateExecutiveSummary(agentResults),
      market_analysis: this.extractMarketAnalysis(agentResults),
      competitive_landscape: this.extractCompetitiveLandscape(agentResults),
      technical_analysis: this.extractTechnicalAnalysis(agentResults),
      tokenomics_analysis: this.extractTokenomicsAnalysis(agentResults),
      growth_metrics: this.extractGrowthMetrics(agentResults),
      recommendations: this.generateRecommendations(agentResults),
      sources: this.aggregateSources(agentResults),
      
      // Metadata
      metadata: {
        generated_at: new Date().toISOString(),
        depth: context.reportDepth,
        agents_used: Object.keys(agentResults),
        success_rate: this.calculateSuccessRate(agentResults),
        project_type: context.projectData?.type || 'unknown',
        blockchain: 'Multiple', // TODO: Extract from agent results
        category: 'DeFi', // TODO: Extract from agent results
        market_cap: 0, // TODO: Extract from onchain data
        tvl: 0, // TODO: Extract from onchain data
      },
    };

    return reportData;
  }

  private generateExecutiveSummary(agentResults: Record<string, AgentResult>): string {
    // Combine key insights from all agents into executive summary
    const insights = [];
    
    for (const [agentType, result] of Object.entries(agentResults)) {
      if (result.success && result.data?.summary) {
        insights.push(`${agentType}: ${result.data.summary}`);
      }
    }

    return insights.join('\n\n');
  }

  private extractMarketAnalysis(agentResults: Record<string, AgentResult>): any {
    return {
      market_positioning: agentResults.market_positioning?.data || {},
      onchain_metrics: agentResults.onchain_analytics?.data?.market_metrics || {}, // Will be empty since onchain is disabled
      competitive_context: agentResults.competitor_analysis?.data?.market_analysis || {},
    };
  }

  private extractCompetitiveLandscape(agentResults: Record<string, AgentResult>): any {
    return agentResults.competitor_analysis?.data?.competitive_landscape || {};
  }

  private extractTechnicalAnalysis(agentResults: Record<string, AgentResult>): any {
    return agentResults.technical_assessment?.data || {};
  }

  private extractTokenomicsAnalysis(agentResults: Record<string, AgentResult>): any {
    return agentResults.tokenomics_analysis?.data || {};
  }

  private extractGrowthMetrics(agentResults: Record<string, AgentResult>): any {
    return {
      onchain_growth: agentResults.onchain_analytics?.data?.growth_metrics || {}, // Will be empty since onchain is disabled
      social_growth: agentResults.social_sentiment?.data?.growth_metrics || {},
      note: 'On-chain analytics temporarily disabled due to context length limitations',
    };
  }

  private generateRecommendations(agentResults: Record<string, AgentResult>): any {
    const recommendations = [];
    
    for (const [agentType, result] of Object.entries(agentResults)) {
      if (result.success && result.data?.recommendations) {
        recommendations.push({
          category: agentType,
          recommendations: result.data.recommendations,
        });
      }
    }

    return recommendations;
  }

  private aggregateSources(agentResults: Record<string, AgentResult>): string[] {
    const allSources = new Set<string>();
    
    for (const result of Object.values(agentResults)) {
      if (result.success && result.sources) {
        result.sources.forEach(source => allSources.add(source));
      }
    }

    return Array.from(allSources);
  }

  private calculateSuccessRate(agentResults: Record<string, AgentResult>): number {
    const total = Object.keys(agentResults).length;
    const successful = Object.values(agentResults).filter(r => r.success).length;
    return total > 0 ? (successful / total) * 100 : 0;
  }

  private async updateReportStatus(reportId: string, status: string): Promise<void> {
    try {
      const updateData: any = { 
        status, 
        updated_at: new Date().toISOString() 
      };
      
      if (status === 'completed') {
        updateData.completed_at = new Date().toISOString();
      }

      await this.db
        .from('reports')
        .update(updateData)
        .eq('id', reportId);
    } catch (error) {
      console.error('Failed to update report status:', error);
    }
  }

  private async updateReportWithResults(reportId: string, reportData: any, formattedReport?: any): Promise<void> {
    try {
      const updateData: any = {
        content: reportData,
        executive_summary: reportData.executive_summary,
        market_analysis: reportData.market_analysis,
        competitive_landscape: reportData.competitive_landscape,
        technical_analysis: reportData.technical_analysis,
        tokenomics_analysis: reportData.tokenomics_analysis,
        growth_metrics: reportData.growth_metrics,
        recommendations: reportData.recommendations,
        sources: reportData.sources,
        updated_at: new Date().toISOString(),
      };

      // Add formatted report content if available
      if (formattedReport) {
        updateData.title = formattedReport.title || updateData.title;
        // Store formatted content in a separate field or merge with existing content
        updateData.content = {
          ...reportData,
          formatted_report: formattedReport,
        };
      }

      await this.db
        .from('reports')
        .update(updateData)
        .eq('id', reportId);

      console.log('✅ Report updated with synthesized results and formatted content');
    } catch (error) {
      console.error('❌ Failed to update report with results:', error);
    }
  }

  private async transformAgentResultsForFrontend(agentResults: Record<string, AgentResult>): Promise<any[]> {
    const agentMappings = {
      'lead_research': { name: 'Lead Research', label: 'Lead Research' },
      'social_sentiment': { name: 'Social Sentiment', label: 'Social Sentiment' },
      'competitor_analysis': { name: 'Competitor Analysis', label: 'Competitor Analysis' },
      'market_positioning': { name: 'Market Positioning', label: 'Market Positioning' },
      'onchain_analytics': { name: 'On-chain Analytics', label: 'On-chain Analytics' },
      'technical_assessment': { name: 'Technical Assessment', label: 'Technical Assessment' },
    };

    const transformedResults = [];

    for (const [agentType, result] of Object.entries(agentResults)) {
      if (!result.success) continue;

      const mapping = agentMappings[agentType as keyof typeof agentMappings] || { name: agentType, label: agentType };
      const agentData = result.data || {};

      // Extract or generate score (0-10 scale)
      const score = this.extractAgentScore(agentData, agentType);
      
      // Extract or generate confidence (0-100 scale)
      const confidence = this.extractAgentConfidence(agentData, agentType);

      const transformedResult = {
        agent_name: agentType,
        agent_label: mapping.label,
        status: 'completed',
        score: score,
        confidence: confidence,
        key_findings: this.extractKeyFindings(agentData, agentType),
        detailed_analysis: this.extractDetailedAnalysis(agentData, agentType),
        recommendations: this.extractRecommendations(agentData),
        risks: this.extractRisks(agentData),
        opportunities: this.extractOpportunities(agentData),
      };

      transformedResults.push(transformedResult);
    }

    return transformedResults;
  }

  private extractAgentScore(agentData: any, agentType: string): number {
    // Try to extract score from various possible locations
    if (agentData.score !== undefined) return Math.min(10, Math.max(0, agentData.score));
    if (agentData.metrics?.score !== undefined) return Math.min(10, Math.max(0, agentData.metrics.score));
    if (agentData.overall_score !== undefined) return Math.min(10, Math.max(0, agentData.overall_score));

    // Generate score based on agent type and data quality
    switch (agentType) {
      case 'social_sentiment':
        return this.calculateSocialSentimentScore(agentData);
      case 'competitor_analysis':
        return this.calculateCompetitorScore(agentData);
      case 'market_positioning':
        return this.calculateMarketPositioningScore(agentData);
      case 'lead_research':
        return this.calculateLeadResearchScore(agentData);
      default:
        return 7.5; // Default score
    }
  }

  private calculateSocialSentimentScore(data: any): number {
    if (!data.metrics) return 6.0;
    
    let score = 5.0; // Base score
    
    // Boost score based on positive sentiment
    if (data.metrics.overall_sentiment?.score > 0.3) score += 2;
    else if (data.metrics.overall_sentiment?.score > 0) score += 1;
    
    // Boost based on community health
    if (data.metrics.community_health?.activity_score > 70) score += 1.5;
    else if (data.metrics.community_health?.activity_score > 50) score += 0.5;
    
    // Boost based on growth metrics
    if (data.metrics.growth_metrics?.follower_growth_rate > 0.1) score += 1;
    
    return Math.min(10, Math.max(0, score));
  }

  private calculateCompetitorScore(data: any): number {
    if (!data.analysis) return 7.0;
    
    let score = 6.0; // Base score
    
    // Add scoring logic based on competitive positioning
    if (data.analysis.market_position === 'leader') score += 2;
    else if (data.analysis.market_position === 'challenger') score += 1;
    
    return Math.min(10, Math.max(0, score));
  }

  private calculateMarketPositioningScore(data: any): number {
    if (!data.analysis) return 7.5;
    
    let score = 6.0; // Base score
    
    // Add scoring based on market opportunity
    if (data.analysis.market_opportunity === 'high') score += 2;
    else if (data.analysis.market_opportunity === 'medium') score += 1;
    
    return Math.min(10, Math.max(0, score));
  }

  private calculateLeadResearchScore(data: any): number {
    if (!data.project_metadata) return 8.0;
    
    let score = 7.0; // Base score for successful research
    
    // Boost based on data completeness
    if (data.project_metadata.description) score += 0.5;
    if (data.project_metadata.website) score += 0.5;
    if (data.project_metadata.social_links) score += 0.5;
    if (data.data_sources && data.data_sources.length > 5) score += 0.5;
    
    return Math.min(10, Math.max(0, score));
  }

  private extractAgentConfidence(agentData: any, agentType: string): number {
    if (agentData.confidence !== undefined) return Math.min(100, Math.max(0, agentData.confidence));
    if (agentData.metrics?.confidence !== undefined) return Math.min(100, Math.max(0, agentData.metrics.confidence));
    
    // Default confidence based on data quality
    const hasData = agentData.metrics || agentData.analysis || agentData.summary;
    return hasData ? 85 : 60;
  }

  private extractKeyFindings(agentData: any, agentType: string): string[] {
    if (agentData.key_findings) return agentData.key_findings;
    if (agentData.findings) return agentData.findings;
    
    // Generate key findings from available data
    const findings = [];
    
    switch (agentType) {
      case 'social_sentiment':
        if (agentData.metrics?.overall_sentiment) {
          findings.push(`Overall sentiment score: ${agentData.metrics.overall_sentiment.score?.toFixed(2) || 'N/A'}`);
        }
        if (agentData.metrics?.community_health) {
          findings.push(`Community activity score: ${agentData.metrics.community_health.activity_score || 'N/A'}`);
        }
        break;
      
      case 'competitor_analysis':
        if (agentData.analysis?.competitive_landscape) {
          findings.push('Competitive landscape analysis completed');
        }
        break;
        
      case 'market_positioning':
        if (agentData.analysis?.market_size) {
          findings.push(`Market size assessment: ${agentData.analysis.market_size}`);
        }
        break;
        
      default:
        if (agentData.summary) {
          findings.push(agentData.summary);
        }
    }
    
    return findings.length > 0 ? findings : ['Analysis completed successfully'];
  }

  private extractDetailedAnalysis(agentData: any, agentType: string): any {
    if (agentData.detailed_analysis) return agentData.detailed_analysis;
    if (agentData.analysis) return agentData.analysis;
    if (agentData.metrics) return agentData.metrics;
    
    // Return structured data based on agent type
    return {
      summary: agentData.summary || 'Analysis completed',
      data_quality: 'Good',
      methodology: `${agentType} analysis using AI agents`,
    };
  }

  private extractRecommendations(agentData: any): string[] {
    if (agentData.recommendations && Array.isArray(agentData.recommendations)) {
      return agentData.recommendations;
    }
    if (agentData.recommendations && typeof agentData.recommendations === 'string') {
      return [agentData.recommendations];
    }
    return ['Continue monitoring metrics and trends'];
  }

  private extractRisks(agentData: any): string[] {
    if (agentData.risks && Array.isArray(agentData.risks)) {
      return agentData.risks;
    }
    if (agentData.risks && typeof agentData.risks === 'string') {
      return [agentData.risks];
    }
    return ['Market volatility may impact performance'];
  }

  private extractOpportunities(agentData: any): string[] {
    if (agentData.opportunities && Array.isArray(agentData.opportunities)) {
      return agentData.opportunities;
    }
    if (agentData.opportunities && typeof agentData.opportunities === 'string') {
      return [agentData.opportunities];
    }
    return ['Potential for growth in emerging markets'];
  }

  private calculateOverallScore(agentsResults: any[]): number {
    if (agentsResults.length === 0) return 0;
    
    const totalScore = agentsResults.reduce((sum, agent) => sum + agent.score, 0);
    return Math.round((totalScore / agentsResults.length) * 100) / 100;
  }
}