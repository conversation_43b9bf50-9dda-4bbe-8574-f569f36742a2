import { generateObject, generateText } from 'ai';
import { getModel } from '@cma/ai';
import { createServerClient } from '@cma/database';
import { z } from 'zod';

export interface AgentContext {
  projectId: string;
  reportId: string;
  userId: string;
  workspaceId: string;
  reportDepth: 'quick' | 'standard' | 'deep';
  projectData?: any;
}

interface TokenLimits {
  maxContextTokens: number;
  maxPromptTokens: number;
}

export interface AgentResult {
  success: boolean;
  data?: any;
  error?: string;
  sources?: string[];
  metrics?: Record<string, any>;
}

export interface AgentProgress {
  step: string;
  progress: number;
  status: 'pending' | 'running' | 'completed' | 'failed';
  message?: string;
}

export abstract class BaseAgent {
  private tokenLimits: TokenLimits = {
    maxContextTokens: 800000, // 800k tokens for context (leaving 200k for prompt)
    maxPromptTokens: 1000000  // 1M total limit
  };
  protected db = createServerClient();
  protected agentType: string;
  protected systemPrompt: string;

  constructor(agentType: string, systemPrompt: string) {
    this.agentType = agentType;
    this.systemPrompt = systemPrompt;
  }

  abstract execute(context: AgentContext, input: any): Promise<AgentResult>;

  protected async updateProgress(
    reportId: string,
    progress: AgentProgress
  ): Promise<void> {
    try {
      await this.db
        .from('agent_runs')
        .update({
          status: progress.status,
          metrics: {
            step: progress.step,
            progress: progress.progress,
            message: progress.message,
          },
          updated_at: new Date().toISOString(),
          ...(progress.status === 'running' && { started_at: new Date().toISOString() }),
          ...(progress.status === 'completed' && { completed_at: new Date().toISOString() }),
        })
        .eq('report_id', reportId)
        .eq('agent_type', this.agentType);
    } catch (error) {
      console.error(`Failed to update progress for ${this.agentType}:`, error);
    }
  }

  protected async logAgentRun(
    reportId: string,
    input: any,
    result: AgentResult
  ): Promise<void> {
    try {
      await this.db.from('agent_runs').insert({
        report_id: reportId,
        agent_type: this.agentType,
        status: result.success ? 'completed' : 'failed',
        input_data: input,
        output_data: result.data,
        error_message: result.error,
        metrics: result.metrics,
        completed_at: new Date().toISOString(),
      });
    } catch (error) {
      console.error(`Failed to log agent run for ${this.agentType}:`, error);
    }
  }

  // Helper function to estimate token count (rough approximation: 1 token ≈ 4 chars)
  private estimateTokenCount(text: string): number {
    return Math.ceil(text.length / 4);
  }

  // Helper function to truncate context data to fit within token limits
  private truncateContext(context: any): any {
    if (!context) return context;

    const contextStr = JSON.stringify(context, null, 2);
    const tokenCount = this.estimateTokenCount(contextStr);

    if (tokenCount <= this.tokenLimits.maxContextTokens) {
      return context;
    }

    console.warn(`🔥 ${this.agentType}: Context too large (${tokenCount} tokens), truncating...`);

    // Strategy 1: Remove large arrays and deeply nested objects
    const truncated = this.deepTruncateObject(context, 3); // Max depth of 3

    const truncatedStr = JSON.stringify(truncated, null, 2);
    const newTokenCount = this.estimateTokenCount(truncatedStr);

    if (newTokenCount <= this.tokenLimits.maxContextTokens) {
      console.log(`✂️ ${this.agentType}: Context truncated to ${newTokenCount} tokens`);
      return truncated;
    }

    // Strategy 2: Keep only essential fields
    const essential = this.extractEssentialData(context);
    console.log(`✂️ ${this.agentType}: Using essential data only (${this.estimateTokenCount(JSON.stringify(essential))} tokens)`);
    return essential;
  }

  // Deep truncate large objects and arrays
  private deepTruncateObject(obj: any, maxDepth: number, currentDepth = 0): any {
    if (currentDepth >= maxDepth || obj === null || typeof obj !== 'object') {
      return obj;
    }

    if (Array.isArray(obj)) {
      // Limit arrays to first 10 items
      return obj.slice(0, 10).map(item => this.deepTruncateObject(item, maxDepth, currentDepth + 1));
    }

    const truncated: any = {};
    for (const [key, value] of Object.entries(obj)) {
      // Skip very large arrays or objects
      if (Array.isArray(value) && value.length > 100) {
        truncated[key] = `[Array with ${value.length} items - truncated]`;
      } else if (typeof value === 'string' && value.length > 5000) {
        truncated[key] = value.substring(0, 1000) + '... [truncated]';
      } else {
        truncated[key] = this.deepTruncateObject(value, maxDepth, currentDepth + 1);
      }
    }
    return truncated;
  }

  // Extract only essential summary data
  private extractEssentialData(context: any): any {
    if (!context || typeof context !== 'object') return context;

    const essential: any = {};

    // Extract key metrics and summary data
    for (const [key, value] of Object.entries(context)) {
      if (key.includes('summary') || key.includes('total') || key.includes('count') || key.includes('score')) {
        essential[key] = value;
      } else if (typeof value === 'object' && value !== null) {
        // For objects, extract only numeric/summary fields
        const summaryObj: any = {};
        for (const [subKey, subValue] of Object.entries(value)) {
          if (typeof subValue === 'number' || (typeof subValue === 'string' && subValue.length < 100)) {
            summaryObj[subKey] = subValue;
          }
        }
        if (Object.keys(summaryObj).length > 0) {
          essential[key] = summaryObj;
        }
      } else if (typeof value === 'number' || (typeof value === 'string' && value.length < 100)) {
        essential[key] = value;
      }
    }

    return essential;
  }

  protected async generateStructuredResponse<T>(
    prompt: string,
    schema: z.ZodSchema<T>,
    context?: any
  ): Promise<T> {
    console.log(`🤖 ${this.agentType}: Generating structured response`);

    try {
      // Truncate context to fit within token limits
      const safeContext = this.truncateContext(context);
      const contextStr = safeContext ? JSON.stringify(safeContext, null, 2) : '';
      const fullPrompt = `${prompt}\n\nContext: ${contextStr}`;
      
      // Final check on prompt size
      const totalTokens = this.estimateTokenCount(fullPrompt);
      if (totalTokens > this.tokenLimits.maxPromptTokens) {
        console.warn(`🔥 ${this.agentType}: Prompt still too large (${totalTokens} tokens), using summary mode`);
        const summaryContext = this.extractEssentialData(safeContext);
        const summaryPrompt = `${prompt}\n\nSummary Data: ${JSON.stringify(summaryContext)}`;
        
        const { object } = await generateObject({
          model: getModel('complex'),
          system: this.systemPrompt,
          prompt: summaryPrompt,
          schema,
        });
        
        console.log(`✅ ${this.agentType}: Structured response generated with summary data`);
        return object;
      }

      const { object } = await generateObject({
        model: getModel('complex'),
        system: this.systemPrompt,
        prompt: fullPrompt,
        schema,
      });

      console.log(`✅ ${this.agentType}: Structured response generated successfully`);
      return object;
    } catch (error) {
      console.error(`❌ ${this.agentType}: Structured response generation failed:`, {
        error: error instanceof Error ? error.message : error,
        agentType: this.agentType,
        prompt: prompt.substring(0, 100) + '...',
      });

      // Re-throw with more context
      throw new Error(`${this.agentType} structured response failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  protected async generateTextResponse(
    prompt: string,
    context?: any
  ): Promise<string> {
    const { text } = await generateText({
      model: getModel('complex'),
      system: this.systemPrompt,
      prompt: `${prompt}\n\nContext: ${JSON.stringify(context, null, 2)}`,
    });

    return text;
  }

  protected async cacheData(
    projectId: string,
    sourceType: string,
    sourceUrl: string,
    data: any,
    expiresInHours: number = 24
  ): Promise<void> {
    const hash = this.generateHash(JSON.stringify(data));
    const expiresAt = new Date();
    expiresAt.setHours(expiresAt.getHours() + expiresInHours);

    try {
      await this.db.from('data_sources').upsert({
        project_id: projectId,
        source_type: sourceType,
        source_url: sourceUrl,
        data,
        hash,
        expires_at: expiresAt.toISOString(),
        last_updated: new Date().toISOString(),
      });
    } catch (error) {
      console.error(`Failed to cache data for ${sourceType}:`, error);
    }
  }

  protected async getCachedData(
    projectId: string,
    sourceType: string,
    sourceUrl: string
  ): Promise<any | null> {
    try {
      const { data } = await this.db
        .from('data_sources')
        .select('data, expires_at')
        .eq('project_id', projectId)
        .eq('source_type', sourceType)
        .eq('source_url', sourceUrl)
        .gte('expires_at', new Date().toISOString())
        .single();

      return data?.data || null;
    } catch (error) {
      return null;
    }
  }

  private generateHash(data: string): string {
    let hash = 0;
    for (let i = 0; i < data.length; i++) {
      const char = data.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    return hash.toString();
  }

  protected async rateLimitWait(delayMs: number = 1000): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, delayMs));
  }

  protected validateInput(input: any, schema: z.ZodSchema): boolean {
    try {
      schema.parse(input);
      return true;
    } catch {
      return false;
    }
  }
}